<mxfile host="app.diagrams.net" modified="2024-08-04T11:30:00.000Z" agent="5.0" etag="ghi789" version="24.6.5" type="device">
  <diagram id="CashFlow" name="Finan<PERSON>n<PERSON> toky">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Client -->
        <mxCell id="2" value="KLIENT&#xa;Zákazník" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="80" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Mistrymont -->
        <mxCell id="3" value="MISTRYMONT&#xa;Hlavní účet" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="480" y="300" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- Internal employees -->
        <mxCell id="4" value="Interní zaměstnanci&#xa;40 osob&#xa;Měsíční mzdy" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="80" y="450" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- External workers -->
        <mxCell id="5" value="Externí montážníci&#xa;150+ OSVČ&#xa;Platba po výstavě" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="260" y="450" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- Partner companies -->
        <mxCell id="6" value="Spřátelené firmy&#xa;Grafika, Kovy&#xa;Zálohové platby" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="440" y="450" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- Material suppliers -->
        <mxCell id="7" value="Dodavatelé materiálu&#xa;jafholz.cz + ostatní&#xa;Průběžné platby" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="620" y="450" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- External accounting -->
        <mxCell id="8" value="Externí účetní&#xa;Fakturace a účetnictví" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="800" y="450" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- Cash flow problem -->
        <mxCell id="9" value="CASH FLOW PROBLÉM&#xa;Prekrývání velkých výstav&#xa;300.000 - 1.000.000 EUR&#xa;Předfinancování před potvrzením" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="80" y="200" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- Payment structure -->
        <mxCell id="10" value="Platební struktura&#xa;60% záloha (flexibilní)&#xa;40% po výstavě&#xa;± doúčtování/srážky" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="320" y="80" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- Budget control -->
        <mxCell id="11" value="Rozpočtová kontrola&#xa;CEO: 100.000 EUR&#xa;PM dostává: 70.000 EUR&#xa;Marže: 30%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="540" y="80" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- Arrows from client -->
        <mxCell id="edge1" edge="1" parent="1" source="2" target="10" style="strokeColor=#00CC00;strokeWidth=3;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge2" edge="1" parent="1" source="10" target="3" style="strokeColor=#00CC00;strokeWidth=3;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Arrows to expenses -->
        <mxCell id="edge3" edge="1" parent="1" source="3" target="4" style="strokeColor=#FF0000;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge4" edge="1" parent="1" source="3" target="5" style="strokeColor=#FF0000;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge5" edge="1" parent="1" source="3" target="6" style="strokeColor=#FF0000;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge6" edge="1" parent="1" source="3" target="7" style="strokeColor=#FF0000;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edge7" edge="1" parent="1" source="3" target="8" style="strokeColor=#FF0000;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Problem indicator -->
        <mxCell id="edge8" edge="1" parent="1" source="9" target="3" style="strokeColor=#FF6600;strokeWidth=2;dashed=1;dashPattern=5 5;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Budget flow -->
        <mxCell id="edge9" edge="1" parent="1" source="11" target="3" style="strokeColor=#0066CC;strokeWidth=2;">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Legend -->
        <mxCell id="12" value="Legenda:&#xa;🟢 Příjmy&#xa;🔴 Výdaje&#xa;🟠 Riziko" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="800" y="200" width="100" height="80" as="geometry" />
        </mxCell>
        
        <!-- Title -->
        <mxCell id="title" value="MISTRYMONT - Finanční toky a Cash Flow" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="400" y="30" width="400" height="30" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>