# ERP Systém - <PERSON><PERSON><PERSON><PERSON><PERSON> Palet a QR Kódů

**Technická Specifikace pro Logistiku Výstavních Stánků**

---

## 1. EXECUTIVE SUMMARY

### 1.1 Účel Systému

Komplexní ERP modul pro správu balení, označování a expedice materiálů určených pro výstavní stánky po celém světě. Systém řeší sledování obsahu palet pomocí QR kódů od balení až po finální expedici.

### 1.2 Kl<PERSON><PERSON><PERSON><PERSON> Lokace

- **Blansko** - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hala (3 QR tiskárny)
- **Rájec** - Centr<PERSON>lní sklad (3 QR tiskárny)

### 1.3 Core Features

- ✅ QR kódování palet s barevným rozlišením
- ✅ Evidence volně loženého materiálu
- ✅ Sledován<PERSON> nakládky kamionů
- ✅ Fotodokumentace poškození
- ✅ Real-time dashboard pro skladové operace

---

## 2. SYSTÉMOVÁ ARCHITEKTURA

### 2.1 Role a Oprávnění Matrix

| Oprávnění | CEO | CTO | System Administrator | Project Manager | Kancelář | Parťák | Montážník Blansko | Skladník |
|-----------|-----|-----|-------------------|-----------------|----------|--------|-------------------|----------|
| **BALENÍ PALET** | | | | | | | | |
| Vytvořit seznam obsahu palety | ✅ | ✅ | ❌ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Vytisknout QR kódy | ✅ | ✅ | ❌ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Editovat obsah palety | ✅ | ✅ | ❌ | ✅ | ✅ | ✅ | ✅ | ✅ |
| **EXPEDICE** | | | | | | | | |
| Naplánovat nakládku kamionů | ✅ | ✅ | ❌ | ✅ | ❌ | ❌ | ❌ | ❌ |
| Skenovat QR při nakládce | ✅ | ✅ | ❌ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Dokumentovat expedici | ✅ | ✅ | ❌ | ✅ | ✅ | ✅ | ✅ | ✅ |
| **SPRÁVA** | | | | | | | | |
| Formulář "Zabalit k expedici" | ✅ | ✅ | ❌ | ✅ | ✅ | ✅ | ❌ | ❌ |
| Změna přiřazení palety ke stánku | ✅ | ✅ | ❌ | ✅ | ✅ | ❌ | ✅ | ✅ |
| Zodpovědný za nářadí | ✅ | ✅ | ❌ | ✅ | ✅ | ✅ | ✅ | ✅ |
| **TECHNICKÁ PODPORA** | | | | | | | | |
| 24/7 Technická podpora | ❌ | ❌ | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Monitoring hardware (tiskárny) | ❌ | ❌ | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Systémová konfigurace | ✅ | ❌ | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |

### 2.2 QR Kód Systém

- **Počet:** 4 QR kódy na paletu
- **Barevné rozlišení:** Automatické přiřazení unikátní barvy pro každý stánek
- **Obsah:** Unikátní ID palety + ID stánku + timestamp + ID balenícího pracovníka
- **Tiskárny:** 6 kusů celkem (Blansko: 3, Rájec: 3)

---

## 3. DETAILNÍ PROCESY

### 3.1 Plánování Expedice

#### 3.1.1 Formulář "Zabalit k Expedici"

**Účel:** Předem definovat co má být zabaleno na konkrétní stánek

**Workflow:**
```
A[CEO/PM/Kancelář/Parťák otevře Stánek] --> B[Klikne na "Zabalit k expedici"]
B --> C[Vyplní seznam požadovaných věcí]
C --> D[Uloží seznam]
D --> E[Seznam se zobrazí v sekci pro balenící pracovníky]
E --> F[Po zabalení se položka označí jako hotová]
F --> G[Zabalená paleta se přesune do "K expedici"]
```

**Oprávnění:** CEO, Project Manager, Kancelář, Parťák

#### 3.1.2 Plánování Nakládky Kamionů

**Proces:**
1. Project Manager zadá termín nakládky do konkrétního stánku
2. Informace o termínu vidí všechny role včetně Montážníka Blansko
3. Systém zobrazí dashboard s přehledem palet připravených k expedici

**Poznámka:** Role "Montážník" (externí pracovník pro montáž na výstavách) nemá přístup do systému.

### 3.2 Balení Palet

#### 3.2.1 Workflow Balení

```
A[Pracovník otevře konkrétní Stánek] --> B[Klikne "Nová paleta k expedici"]
B --> C[Vyplní formulář obsahu palety]
C --> D[Zadá rozměry palety A×B×H mm]
D --> E[Zadá orientační hmotnost]
E --> F[Vybere tiskárnu pro QR kódy]
F --> G[Systém vygeneruje 4 QR kódy s barvou stánku]
G --> H[Vytiskne QR kódy]
H --> I[Nalepí 4 QR kódy na paletu]
I --> J[Paleta se zařadí do "K expedici"]
```

#### 3.2.2 Formulář Obsahu Palety

**Povinná pole:**
- Seznam věcí/materiálů (textové pole, multiple items)
- Rozměry palety (A × B × H v mm)
- Orientační hmotnost (kg)
- Výběr tiskárny (dropdown)
- Přiřazení ke stánku (automatické dle kontextu)

#### 3.2.3 Typy Balení

**Kompletní paleta (Blansko):**
- Paleta zabalená a připravená k expedici
- QR kódy vytisknuty ihned
- Odeslání na sklad Rájec

**Neúplná paleta (Blansko → Rájec):**
- Paleta bez QR kódů
- Dokompletování na skladě Rájec
- Vytištění QR kódů až po dokompletování

### 3.3 Evidence Volně Loženého Materiálu

#### 3.3.1 Kdy Evidovat

- Materiál nelze zabalit na paletu
- Rozměry nebo charakter materiálu neumožňuje paletizaci
- Dodatečný materiál během realizace

#### 3.3.2 Workflow

```
A[Pracovník identifikuje volně ložený materiál] --> B[Otevře konkrétní Stánek]
B --> C[Klikne "Volně ložené k expedici"]
C --> D[Vyplní seznam volně loženého materiálu]
D --> E[Uloží záznam]
E --> F[Materiál se zobrazí ve složce stánku]
F --> G[Při nakládce se skenuje/eviduje zvlášť]
```

### 3.4 Nakládání Kamionů

#### 3.4.1 Proces Nakládky

```
A[Zodpovědná osoba zahájí nakládku] --> B[Naskenuje QR kód každé palety]
B --> C[Systém ověří příslušnost ke stánku]
C --> D[Eviduje volně ložené věci ručně]
D --> E[Po naložení poslední palety]
E --> F[Potvrdí konec nakládky]
F --> G[Vyfotí SPZ kamionu]
G --> H[Systém vytvoří složku "Kamion k expedici"]
H --> I[Kamion obsahuje kompletní seznam palet + volně ložené]
```

#### 3.4.2 Skenování QR Kódů

- Každá role má možnost skenovat QR kódy
- Real-time ověření příslušnosti ke stánku
- Automatické odečítání z celkového počtu palet ke stánku
- Visual feedback (zelená/červená) při skenování

### 3.5 Expedice a Sledování

#### 3.5.1 Dashboard Skladu

**Zobrazuje:**
- QR kódy seskupené podle stánků (barevně rozlišené)
- Počet palet připravených k expedici
- Počet palet zbývajících k expedici
- Status nakládky kamionů
- Termíny plánovaných expedic

---

## 4. SPRÁVA A AKTUALIZACE OBSAHU

### 4.1 Doplňování Materiálu do Palet

#### 4.1.1 Workflow Aktualizace

```
A[Pracovník potřebuje změnit obsah palety] --> B[Naskenuje QR kód palety]
B --> C[Otevře se detail palety]
C --> D[Upraví seznam položek]
D --> E[Přidá/odebere položky]
E --> F[Stiskne "Aktualizovat"]
F --> G[Systém aktualizuje data globálně]
```

**Oprávnění:** Skladník, Montážník Blansko (všechny role s oprávněním balení)

### 4.2 Změna Přiřazení Palety ke Stánku

#### 4.2.1 Proces Přeřazení

```
A[Oprávněná osoba otevře stánek s paletou] --> B[Najde paletu k přeřazení]
B --> C[Klikne "Změna expedice"]
C --> D[Vybere cílový stánek z dropdown]
D --> E[Potvrdí změnu]
E --> F[QR kód se odebere z původního stánku]
F --> G[QR kód se přidá k novému stánku]
G --> H[Barevné označení se změní]
```

**Oprávnění:** CEO, Project Manager, Kancelář, Montážník Blansko, Skladník

### 4.3 Řešení Poškození

#### 4.3.1 Workflow Poškození

```
A[Objevení poškození při nakládce] --> B[Skladník vybere "Vyfotit poškozené"]
B --> C[Naskenuje QR kód poškozené palety]
C --> D[Systém automaticky identifikuje paletu a stánek]
D --> E[Bude vyzván k pořízení fotografie]
E --> F[Přidá komentář]
F --> G[Uloží záznam]
G --> H[Foto se uloží do "Poškozené věci při expedici" v konkrétním stánku]
H --> I[Automatická notifikace CEO, CTO, PM a Parťákovi stánku]
I --> J[Možnost přebalení palety a nový tisk QR]
```

#### 4.3.2 Přebalení Poškozené Palety

- Možnost vytisknout stejný QR kód znovu
- Zachování integrity dat v systému
- Evidence přebalení v logu palety

---

## 5. TECHNICKÉ POŽADAVKY

### 5.1 Hardware Specifikace

#### 5.1.1 QR Tiskárny

**Celkový počet:** 6 kusů

**Rozmístění:**
- Blansko (Výroba): 3 tiskárny
- Rájec (Sklad): 3 tiskárny

**Technické požadavky:**
- Tisk barevných QR kódů
- Samolepicí etikety
- Síťové připojení pro remote tisk
- Rychlost tisku min. 5 etiket/minutu

#### 5.1.2 Skenery/Mobilní Zařízení

- Podpora QR skenování přes mobilní aplikaci
- Camera API integrace
- Offline/online synchronizace dat

### 5.2 QR Kód Specifikace

#### 5.2.1 Obsah QR Kódu

```json
{
  "pallet_id": "PAL-2025-001234",
  "booth_id": "BOOTH-CES-2025-15",
  "booth_color": "#FF6B35",
  "created_timestamp": "2025-06-13T14:30:00Z",
  "packed_by_user_id": "USER-123",
  "location": "RAJEC",
  "status": "PACKED"
}
```

#### 5.2.2 Barevné Rozlišení

- **Automatické přiřazení:** Při vytvoření nového stánku
- **Unikátnost:** Každý stánek má svou barvu
- **Vizuální rozlišení:** Vysoký kontrast pro snadné rozpoznání
- **Persistence:** Barva se zachovává po celou dobu existence stánku

### 5.3 Rozměry a Specifikace Palet

#### 5.3.1 Formát Rozměrů

- **Jednotka:** Milimetry (mm)
- **Formát:** A × B × H (délka × šířka × výška)
- **Příklad:** 1200×800×1500 = 1,2m × 0,8m × 1,5m
- **Dodatečně:** Orientační hmotnost v kg

---

## 6. USER INTERFACE SPECIFIKACE

### 6.1 Dashboard Design

#### 6.1.1 Hlavní Dashboard Skladu

```
┌─────────────────────────────────────────────────┐
│ PŘEHLED SKLADU - PALETY K EXPEDICI              │
├─────────────────────────────────────────────────┤
│ 🔵 STÁNEK: CES Las Vegas 2025      [12 palet]  │
│ ├─ PAL-001 ✅  ├─ PAL-002 ✅  ├─ PAL-003 ⏳    │
│ └─ Termín nakládky: 15.6.2025                   │
├─────────────────────────────────────────────────┤
│ 🟢 STÁNEK: IFA Berlin 2025          [8 palet]  │
│ ├─ PAL-015 ✅  ├─ PAL-016 ✅  ├─ PAL-017 ✅    │
│ └─ Termín nakládky: 18.6.2025                   │
├─────────────────────────────────────────────────┤
│ 🔴 STÁNEK: MWC Barcelona 2025       [5 palet]  │
│ ├─ PAL-023 ⏳  ├─ PAL-024 ❌  ├─ PAL-025 ⏳    │
│ └─ Termín nakládky: 20.6.2025                   │
└─────────────────────────────────────────────────┘
```

#### 6.1.2 Detail Stánku

```
┌─────────────────────────────────────────────────┐
│ STÁNEK: CES Las Vegas 2025 🔵                   │
├─────────────────────────────────────────────────┤
│ 📋 ZABALIT K EXPEDICI                           │
│ ├─ Grafické panely 3×2m ⏳                      │
│ ├─ LED pásky 50m ✅                             │
│ └─ Prezentační TV 55" ⏳                        │
├─────────────────────────────────────────────────┤
│ 📦 PALETY K EXPEDICI (12 kusů)                  │
│ ├─ PAL-001: Konstrukce (1200×800×1500) ✅       │
│ ├─ PAL-002: Elektro (1000×1000×1200) ✅         │
│ └─ PAL-003: Grafika (800×600×200) ⏳            │
├─────────────────────────────────────────────────┤
│ 📋 VOLNĚ LOŽENÉ MATERIÁLY                       │
│ ├─ Koberec 4×6m                                 │
│ └─ Skleněné vitríny 2ks                         │
├─────────────────────────────────────────────────┤
│ 🚛 EXPEDICE                                     │
│ ├─ Termín nakládky: 15.6.2025                   │
│ ├─ Status: Připraveno k nakládce                │
│ └─ Zodpovědný za nářadí: Jan Novák              │
└─────────────────────────────────────────────────┘
```

### 6.2 Mobilní Rozhraní

#### 6.2.1 Skenování QR (Mobile)

- Velké tlačítko "SKENOVAT QR"
- Real-time camera preview
- Visual feedback při úspěšném skenu
- Audio potvrzení skenování
- Offline mode s pozdější synchronizací

#### 6.2.2 Formulář Obsahu Palety (Mobile-friendly)

- Touch-optimized interface
- Dynamické přidávání položek
- Dropdown výběr tiskárny
- Validace rozměrů v real-time

### 6.3 Fotodokumentace

#### 6.3.1 Interface pro Poškození

```
┌─────────────────────────────────────────────────┐
│ 📸 VYFOTIT POŠKOZENÉ                            │
├─────────────────────────────────────────────────┤
│ Stánek: [Dropdown výběr stánku]                 │
│ ┌─────────────────────────────────────────────┐   │
│ │                                             │   │
│ │          CAMERA PREVIEW                     │   │
│ │                                             │   │
│ └─────────────────────────────────────────────┘   │
│ Komentář: ____________________________         │
│ [                 VYFOTIT                 ]       │
└─────────────────────────────────────────────────┘
```

---

## 7. BUSINESS LOGIC A VALIDACE

### 7.1 Validační Pravidla

#### 7.1.1 Palety

- Minimálně 1 položka v seznamu obsahu
- Rozměry: A, B, H > 0 a < 5000mm
- Hmotnost: > 0 a < 2000kg
- Tiskárna: musí být online a dostupná

#### 7.1.2 QR Kódy

- Unikátnost ID v rámci celého systému
- Platnost 2 roky od vytvoření
- Možnost regenerace při poškození

#### 7.1.3 Expedice

- Nelze expedovat stánek bez termínu nakládky
- Všechny palety musí být naskenované před potvrzením expedice
- Povinná fotka SPZ kamionu

### 7.2 Notifikace a Upozornění

#### 7.2.1 Automatické Notifikace

- **Poškození:** CEO + CTO + Project Manager + Parťák stánku
- **Dokončení balení:** Dashboard update
- **Expedice:** Všichni stakeholders stánku
- **Chybějící materiál:** Upozornění 24h před nakládkou
- **Technické problémy:** System Administrator (24/7)

#### 7.2.2 Email/SMS Šablony

```
Předmět: 🚨 Poškození materiálu - [STÁNEK]
Tělo: Byl nalezen poškozený materiál pro stánek [NÁZEV STÁNKU].
Poškozená položka: [POPIS]
Paleta: [ID PALETY]
Datum: [TIMESTAMP]
Komentář: [USER COMMENT]
Kontaktujte prosím skladníka pro řešení.
```

---

## 8. TECHNICKÁ IMPLEMENTACE

### 8.1 Database Schema (návrh)

#### 8.1.1 Hlavní Tabulky

```sql
-- Stánky
booths (
    id, name, color, status,
    loading_date, project_manager_id,
    partak_id, responsible_for_tools_id
)

-- Palety
pallets (
    id, booth_id, qr_code, dimensions,
    weight, status, created_at, location,
    printer_used
)

-- Obsah palet
pallet_items (
    id, pallet_id, item_name,
    quantity, description
)

-- Volně ložené věci
loose_items (
    id, booth_id, item_name,
    description, status
)

-- Kamiony
trucks (
    id, license_plate, loading_date,
    status, photo_path
)

-- Expedice
truck_pallets (
    truck_id, pallet_id, scanned_at,
    loaded_by_user_id
)
```

### 8.2 API Endpoints (návrh)

#### 8.2.1 Core Operations

```
POST /api/pallets              # Vytvoření nové palety
GET  /api/pallets/{id}         # Detail palety
PUT  /api/pallets/{id}         # Aktualizace obsahu
POST /api/pallets/{id}/qr      # Generování QR
POST /api/pallets/scan         # Skenování QR
POST /api/trucks               # Zahájení nakládky
POST /api/trucks/{id}/complete # Dokončení nakládky
POST /api/trucks/{id}/photo    # Upload foto SPZ
GET  /api/booths/{id}/dashboard # Dashboard stánku
POST /api/damage-report         # Hlášení poškození
```

### 8.3 Integrace s Hardware

#### 8.3.1 Tiskárny

- Network printing protocol
- Queue management pro multiple requests
- Status monitoring (online/offline, papír, toner)
- Error handling a retry logic

#### 8.3.2 Kamery/Skenery

- Cross-platform camera API
- QR kod detection optimalizace
- Batch processing pro multiple skeny

---

## 9. TESTOVACÍ SCÉNÁŘE

### 9.1 User Stories pro Testing

#### 9.1.1 Základní Workflow

**Jako skladník v Blansku**  
Chci zabalit paletu pro CES výstavu  
Abych mohl vytvořit seznam obsahu a vytisknout QR kódy

**Acceptance Criteria:**
- ✅ Mohu vybrat stánek "CES Las Vegas 2025"
- ✅ Mohu přidat položky do seznamu obsahu
- ✅ Mohu zadat rozměry 1200×800×1500
- ✅ Mohu vybrat tiskárnu "Blansko-01"
- ✅ Systém vygeneruje 4 QR kódy s modrou barvou
- ✅ QR kódy se vytisknou na vybrané tiskárně

#### 9.1.2 Expedice Scenario

**Jako expedient**  
Chci naložit kamion pro konkrétní stánek  
Abych mohl naskenovat všechny palety a dokončit expedici

**Acceptance Criteria:**
- ✅ Vidím seznam palet připravených pro CES stánek
- ✅ Mohu skenovat QR kód každé palety
- ✅ Systém ověří příslušnost palety ke stánku
- ✅ Mohu evidovat volně ložené věci
- ✅ Mohu vyfotit SPZ kamionu
- ✅ Kamion se označí jako "expedován"

### 9.2 Edge Cases

#### 9.2.1 Chybové Stavy

- Tiskárna offline během tisku QR
- Skenování neexistujícího QR kódu
- Pokus o expedici bez termínu nakládky
- Změna přiřazení palety během nakládky
- Poškození palety s již vytištěnými QR kódy

#### 9.2.2 Performance Testing

- 100+ palet současně na dashboardu
- Multiple users skenující QR současně
- Batch printing 50+ QR kódů
- Mobile app responsiveness při pomalém internetovém připojení

---

## 10. DEPLOYMENT A MAINTENANCE

### 10.1 Deployment Checklist

#### 10.1.1 Pre-deployment

- Database migration scripts
- Hardware installation (6 tiskáren)
- Network connectivity test
- User account setup
- Permission assignment
- Mobile app distribution

#### 10.1.2 Go-live Support

- On-site training pro skladníky
- Documentation handover
- 24/7 support first week
- Performance monitoring setup
- Backup procedures verification

### 10.2 Maintenance Procedures

#### 10.2.1 Routine Maintenance

- Týdenní backup dat
- Monitor tiskáren status
- QR kód validace (expired codes cleanup)
- Performance metrics review
- User feedback collection

#### 10.2.2 Emergency Procedures

- Tiskárna failure - alternative printing
- Network outage - offline mode activation
- Database corruption - backup restore
- QR kod čtečka failure - manual entry fallback

---

## 11. KONTAKT A PODPORA

**Projekt:** ERP Systém - Správa Palet a QR Kódů  
**Verze:** 1.0  
**Datum:** 13. června 2025  
**Status:** Ready for Development

**Klíčoví Stakeholders:**
- Project Manager: [Jméno]
- ERP Developer: [Jméno]
- Skladník Blansko: [Jméno]
- Skladník Rájec: [Jméno]

---

*Tento dokument obsahuje kompletní specifikaci pro implementaci ERP modulu. Pro další dotazy nebo clarifikace kontaktujte project managera.*

---

*Dokument vytvořen: 13. června 2025*  
*Technická specifikace: ERP systém - Správa palet a QR kódů*
